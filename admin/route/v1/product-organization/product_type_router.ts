/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminProductTypeController = () =>
  import('#adminControllers/product-organization/product_type_controller')

export default function adminProductTypeRoutes() {
  router
    .group(() => {
      router.get('product-type/select', [AdminProductTypeController, 'select'])

      router.resource('product-type', AdminProductTypeController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
