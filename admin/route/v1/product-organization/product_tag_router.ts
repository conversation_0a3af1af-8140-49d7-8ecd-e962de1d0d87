/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminProductTagController = () =>
  import('#adminControllers/product-organization/product_tag_controller')

export default function adminProductTagRoutes() {
  router
    .group(() => {
      router.get('product-tag/select', [AdminProductTagController, 'select'])

      router.resource('product-tag', AdminProductTagController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
