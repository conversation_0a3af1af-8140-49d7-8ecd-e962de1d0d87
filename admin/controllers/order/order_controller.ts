import ZnOrder from '#models/zn_order'
import type { HttpContext } from '@adonisjs/core/http'
// import { createOrderValidator } from '../../validators/order/order_validator.js'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import {
  createOrderValidator,
  updateOrderValidator,
} from '../../validators/order/order_validator.js'
import ZnAddress from '#models/zn_address'
import queue from '@rlanz/bull-queue/services/main'
import CheckUnfulfillOrdersJob from '#jobs/check_unfulfill_orders_job'

export default class AdminOrderController {
  /**
   * @index
   * @tag Admin Order
   * @summary Read all orders
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnOrder[]>.append("id":"").paginated() - Read all orders descriptively
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ORDER)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnOrder.query().whereNull('deletedAt').preload('user')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw(`LOWER(name) LIKE LOWER(?)`, [`%${search}%`])
        })
      }

      query.orderBy('createdAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Order
   * @summary Return info for creation
   * @responseBody 200 - {"info":{"permissions":["ZnPermission"]}} - Return info for creation descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ORDER)

    try {
      return response.ok({
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Order
   * @summary Create a order
   * @requestBody <ZnOrder>.append("permissionIds":[""])
   * @responseBody 201 - <ZnOrder>.append("id":"","createdAt":"","updatedAt":"")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]}
   * @responseBody 401 - Unauthorized access
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ORDER)

    const data = request.all()

    const payload = await createOrderValidator.validate(data)
    payload

    try {
      // const created = await ZnOrder.create({
      //     // name: payload.name,
      // })
      //   if (payload.permissionIds && payload.permissionIds.length > 0) {
      //     await created.related('permissions').sync(payload.permissionIds)
      //   }
      // return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Order
   * @summary Read order
   * @paramPath id - ID of Order - @type(string) @required
   * @responseBody 200 - <ZnOrder>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a order descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ORDER)

    const orderId = params.id

    const order = await ZnOrder.query()
      .where('id', orderId)
      .preload('orderDetails', (orderDetailQuery) => {
        orderDetailQuery.preload('variant', (variantQuery) => {
          variantQuery
            .preload('image')
            .preload('product')
            .preload('optionValues', (optionValueQuery) => {
              optionValueQuery.preload('option')
            })
        })
      })
      .withCount('orderDetails', (orderDetailQuery) => orderDetailQuery.sum('quantity'))
      .first()

    if (!order) {
      return response.notFound({ message: 'Order not found' })
    }

    return response.ok(order)
  }

  /**
   * @edit
   * @tag Admin Order
   * @summary Return info for updating
   * @responseBody 200 - {"data":"<ZnOrder>","permissions":{"orders":["ZnPermission"]}} - Return info for updating descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ORDER)

    try {
      const orderId = params.id

      const order = await ZnOrder.query()
        .where('id', orderId)
        .preload('orderDetails', (orderDetailQuery) => {
          orderDetailQuery.preload('variant', (variantQuery) => {
            variantQuery
              .preload('image')
              .preload('product')
              .preload('optionValues', (optionValueQuery) => {
                optionValueQuery.preload('option')
              })
          })
        })
        .withCount('orderDetails', (orderDetailQuery) => orderDetailQuery.sum('quantity'))
        .preload('user')
        .preload('billing')
        .preload('shipping')
        .first()

      if (!order) {
        return response.notFound({ message: 'Order not found' })
      }

      return response.ok({
        data: {
          ...order?.serialize(),
        },
        info: {},
      })
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Order
   * @summary Update order
   * @description Update order descriptively
   * @paramPath id - ID of Order - @type(string) @required
   * @requestBody <ZnOrder>.append("permissionIds":[""])
   * @responseBody 200 - <ZnOrder>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ORDER)

    const orderId = params.id

    const order = await ZnOrder.query().where('id', orderId).first()

    if (!order) {
      return response.notFound({ message: 'Order not found' })
    }

    const data = request.all()

    const payload = await updateOrderValidator.validate(data)

    try {
      order.name = payload.name
      order.email = payload.email
      order.userId = payload.userId || null

      if (payload.billingId) {
        order.billingId = payload.billingId
      } else if (payload.billing) {
        const billing = await ZnAddress.create({
          ...payload.billing,
        })
        order.billingId = billing.id
      }

      if (payload.sameBillShip) {
        order.shippingId = order.billingId
      } else if (payload.shippingId) {
        order.shippingId = payload.shippingId
      } else if (payload.shipping) {
        const shipping = await ZnAddress.create({
          ...payload.shipping,
        })
        order.shippingId = shipping.id
      }

      order.fulfillmentStatus = payload.fulfillmentStatus
      order.financialStatus = payload.financialStatus

      const updated = await order.save()

      return response.ok(updated)
    } catch (error) {
      console.log(error)

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Order
   * @summary Soft-delete a order
   * @description Soft-delete a order descriptively
   * @paramPath id - ID of Order - @type(string) @required
   * @responseBody 200 - {"message":"Order soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.ORDER)

    const orderId = params.id

    const order = await ZnOrder.find(orderId)

    if (!order) {
      return response.notFound({ message: 'Order not found' })
    }

    await order.softDelete()

    return response.ok({ message: 'Order soft-deleted successfully' })
  }

  /**
   * @checkUnfulfill
   * @tag Admin Order
   * @summary Trigger check unfulfill orders job
   * @responseBody 200 - Job dispatched successfully
   */
  async checkUnfulfill({ response }: HttpContext) {
    try {
      await queue.dispatch(CheckUnfulfillOrdersJob, { days: 7 })

      return response.ok({
        message: 'Send unfulfill orders notification dispatched successfully',
      })
    } catch (error) {
      console.error('Error dispatching check unfulfill orders job:', error)
      return response.internalServerError({
        message: 'Failed to dispatch check unfulfill orders job',
        error: error.message,
      })
    }
  }
}
