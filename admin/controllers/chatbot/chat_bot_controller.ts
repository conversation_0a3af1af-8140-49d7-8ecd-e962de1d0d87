// /****************************************************************************************
//  * Chat<PERSON><PERSON>Ad<PERSON><PERSON>ontroller
//  * A back-office companion to `<PERSON><PERSON><PERSON>otController` that lets staff moderate / participate
//  * in every bot room without the per-user restrictions that apply to customers.
//  *
//  * Key differences vs ChatBotController
//  * ────────────────────────────────────
//  * • Auth: uses `ZnAdmin` guard (admin token) instead of end-user JWT.
//  * • Token: gives admins SEND_MESSAGE | DISCONNECT_USER | DELETE_MESSAGE powers.
//  * • Message writes: sender recorded as `{ adminId }`; no owner checks on clear/delete.
//  * • Extras: endpoint to list all bot rooms (paginated   & ordered by latest activity).
//  ****************************************************************************************/
//
// import ChatMessageJob from '#jobs/chat_message_job'
// import ZnAdmin from '#models/zn_admin'
// import ZnChatMessage from '#models/zn_chat_message'
// import ZnChatRoom from '#models/zn_chat_room'
// import ZnCollection from '#models/zn_collection'
// import ZnPost from '#models/zn_post'
// import ZnProduct from '#models/zn_product'
// import { IvschatService } from '#services/aws/ivschat_service'
// import { HttpContext } from '@adonisjs/core/http'
// import db from '@adonisjs/lucid/services/db'
// import queue from '@rlanz/bull-queue/services/main'
//
// export default class ChatBotAdminController {
//   private ivsChatService: IvschatService
//
//   constructor() {
//     this.ivsChatService = new IvschatService()
//   }
//
//   /* -------------------------------------------------------------------------- */
//   /*                              ROOM-LEVEL ENDPOINTS                          */
//   /* -------------------------------------------------------------------------- */
//
//   /**
//    * @getChatRooms
//    * @tag Chat Bot Admin
//    * @summary Paginated list of all bot chat rooms (latest activity first)
//    */
//   async getChatRooms({ request, response }: HttpContext) {
//     const { page = 1, limit = 10 } = request.qs()
//
//     // order by newest message, fall back to room creation
//     const rooms = await db
//       .from('zn_chat_rooms')
//       .leftJoin('zn_chat_messages', 'zn_chat_rooms.id', 'zn_chat_messages.roomId')
//       .whereNull('zn_chat_rooms.deletedAt')
//       .groupBy('zn_chat_rooms.id')
//       .orderByRaw('COALESCE(MAX(zn_chat_messages.createdAt), zn_chat_rooms.createdAt) DESC')
//       .select('zn_chat_rooms.*')
//       .paginate(page, limit)
//
//     return response.ok(rooms)
//   }
//
//   /**
//    * @createChatToken
//    * @tag Chat Bot Admin
//    * @summary Provide IVS Chat token with admin-level capabilities
//    */
//   async createChatToken({ auth, params, response }: HttpContext) {
//     const roomId = params.id
//     const room = await ZnChatRoom.find(roomId)
//     if (!room) return response.notFound({ message: 'Chat Room Not Found' })
//
//     const admin = auth.getUserOrFail() as ZnAdmin
//     await admin.load('avatar')
//
//     const token = await this.ivsChatService.createChatToken({
//       roomArn: room.arn,
//       userId: admin.id,
//       capabilities: ['SEND_MESSAGE', 'DISCONNECT_USER', 'DELETE_MESSAGE'],
//       attributes: {
//         type: 'admin',
//         username: admin.username,
//         name: admin.name,
//         avatarUrl: admin.avatar?.url,
//       },
//     })
//
//     return response.ok(token)
//   }
//
//   /**
//    * @getChatRoom
//    * @tag Chat Bot Admin
//    * @summary Fetch a single room (incl. messages) by ID
//    */
//   async getChatRoom({ params, response }: HttpContext) {
//     const room = await ZnChatRoom.query()
//       .where({ id: params.id })
//       .preload('messages', (q) =>
//         q
//           .preload('user', (u) => u.preload('avatarMedia'))
//           .preload('admin', (a) => a.preload('avatar'))
//           .orderBy('createdAt', 'desc'),
//       )
//       .first()
//
//     if (!room) return response.notFound('Chat Room Not Found')
//     return response.ok(room)
//   }
//
//   /* -------------------------------------------------------------------------- */
//   /*                                MESSAGE FLOW                                */
//   /* -------------------------------------------------------------------------- */
//
//   /**
//    * @createChatMessage
//    * @tag Chat Bot Admin
//    * @summary Post a message as staff into any bot chat room
//    */
//   async createChatMessage({ auth, params, request, response }: HttpContext) {
//     const room = await ZnChatRoom.find(params.id)
//     if (!room) return response.notFound('Chat Room Not Found')
//
//     const admin = auth.getUserOrFail() as ZnAdmin
//     const data = request.body()
//
//     await queue.dispatch(
//       ChatMessageJob,
//       {
//         roomId: room.id,
//         data: {
//           ...data,
//           sender: { adminId: admin.id },
//         },
//       },
//       { queueName: 'chatBot' },
//     )
//
//     return response.ok('Sent')
//   }
//
//   /**
//    * @getChatMessages
//    * @tag Chat Bot Admin
//    * @summary List / search messages in a bot room
//    */
//   async getChatMessages({ params, request, response }: HttpContext) {
//     const { search = '', page = 1, limit = 10 } = request.qs()
//
//     const roomId = params.id
//     const room = await ZnChatRoom.find(roomId)
//     if (!room) return response.notFound('Chat Room Not Found')
//
//     const query = ZnChatMessage.query()
//       .where({ roomId })
//       .preload('user', (u) => u.preload('avatarMedia'))
//       .preload('admin', (a) => a.preload('avatar'))
//       .orderBy('createdAt', 'desc')
//
//     if (search) {
//       const like = `%${search.replace(/\s+/g, '%')}%`
//       query.whereILike('content', like)
//     }
//
//     const result = await query.paginate(page, limit)
//     return response.ok(result)
//   }
//
//   /**
//    * @clearChatMessages
//    * @tag Chat Bot Admin
//    * @summary Wipe all messages in a bot room (no ownership check)
//    */
//   async clearChatMessages({ params, response }: HttpContext) {
//     const room = await ZnChatRoom.find(params.id)
//     if (!room) return response.notFound('Chat Room Not Found')
//
//     await ZnChatMessage.query().where({ roomId: room.id }).delete() // hard-delete for admins
//     return response.ok('Chat messages cleared')
//   }
//
//
//
//   async listChatResources({ request, response }: HttpContext) {
//     const { productIds, collectionIds, postIds } = request.body()
//
//     const parse = (v?: string | string[]) =>
//       !v ? [] : Array.isArray(v) ? v : JSON.parse(v)
//
//     const products = parse(productIds).length
//       ? await ZnProduct.query()
//         .whereIn('id', parse(productIds))
//         .whereNot('status', 'draft')
//         .where('isGift', false)
//         .preload('variant')
//         .preload('image')
//         .preload('reviewsSummary')
//       : undefined
//
//     const collections = parse(collectionIds).length
//       ? await ZnCollection.query()
//         .whereIn('id', parse(collectionIds))
//         .where('status', true)
//       : undefined
//
//     const posts = parse(postIds).length
//       ? await ZnPost.query()
//         .whereIn('id', parse(postIds))
//         .where({ expired: false, isDraft: false, isUnlist: false })
//         .preload('thumbnail')
//         .preload('medias')
//       : undefined
//
//     return response.ok({ products, collections, posts })
//   }
// }
