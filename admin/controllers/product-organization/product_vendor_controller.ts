import ZnVendor from '#models/zn_vendor'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import {
  createProductVendorValidator,
  updateProductVendorValidator,
} from '../../validators/product/product_validator.js'

export default class AdminProductVendorController {
  /**
   * @index
   * @tag Admin Product-Vendor
   * @summary Read all products
   * @paramQuery page - Page Num ber (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnProduct[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all produtcs descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnVendor.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @select
   * @tag Admin Product-Vendor
   * @summary Read all products
   * @paramQuery field - Search field - @type(string)
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnProduct[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all produtcs descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async select({ request, response }: HttpContext) {
    const { search, filter } = request.qs()

    try {
      const query = ZnVendor.query()

      if (search) {
        query.whereRaw(`LOWER(companyName) LIKE LOWER(?)`, [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(1, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @show
   * @tag Admin Product-Vendor
   * @summary Read a product
   * @paramPath id - ID of Product - @type(string) @required
   * @responseBody 200 - <ZnProduct>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a product descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Show individual record
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    try {
      const productId = params.id

      const product = await ZnVendor.query().where('id', productId).first()

      if (!product) {
        response.notFound({ message: 'Vendor not found' })
      }

      return response.ok(product)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Product-Vendor
   * @summary Return data to create a product
   */
  /**
   * Display form to create a new record
   */
  async create({ }: HttpContext) { }

  /**
   * @store
   * @tag Admin Product-Vendor
   * @summary Create a product
   * @requestBody <ZnProduct>
   * @responseBody 201 - <ZnProduct>.append("id":"","createdAt":"","updatedAt":"")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]}
   * @responseBody 401 - Unauthorized access
   */
  /**
   * Handle form submission for the create action
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.PRODUCT)

    const data = request.all()

    const payload = await createProductVendorValidator.validate(data)

    try {
      const created = await ZnVendor.create({
        companyName: payload.name,
      })

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @edit
   * @tag Admin Product-Vendor
   * @summary Return data to update a product
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    try {
      const vendorId = params.id

      const vendor = await ZnVendor.query().where('id', vendorId).first()

      return response.ok({
        data: {
          ...vendor?.serialize(),
        },
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Product-Vendor
   * @summary Update a product
   * @description Update a product descriptively
   * @paramPath id - ID of Product - @type(string) @required
   * @requestBody <ZnProduct>
   * @responseBody 200 - <ZnProduct>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

    const vendorId = params.id

    const vendor = await ZnVendor.query().where('id', vendorId).first()

    if (!vendor) {
      return response.notFound({ message: 'Vendor not found' })
    }

    const data = request.all()

    const payload = await updateProductVendorValidator(vendorId).validate(data)

    try {
      vendor.companyName = payload.name || vendor.companyName

      const updated = await vendor.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }
  /**
   * @destroy
   * @tag Admin Product-Vendor
   * @summary Soft-delete a product
   * @description Soft-delete a product descriptively
   * @paramPath id - ID of Product - @type(string) @required
   * @responseBody 200 - {"message":"Product soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Delete record
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.PRODUCT)

    try {
      const vendorId = params.id

      const vendor = await ZnVendor.query().where('id', vendorId).first()

      if (!vendor) {
        return response.notFound({ message: 'Vendor not found' })
      }

      await vendor?.delete()

      return response.ok({ message: 'Vendor soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
