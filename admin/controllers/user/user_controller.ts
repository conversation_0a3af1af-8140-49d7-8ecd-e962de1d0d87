import ZnUser from '#models/zn_user'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { AuthService } from '../../../services/auth/auth_service.js'
import { createUserValidator, updateUserValidator } from '../../validators/user/user_validator.js'
import { ShopifyAuthService } from '../../../services/shopify/auth/shopify_auth_service.js'
import { DateTime } from 'luxon'
import { SmileRewardService } from '#services/smile_reward_service'
import ZnMedia from '#models/zn_media'

export default class AdminUserController {
  private authService: AuthService
  private shopifyAuthService: ShopifyAuthService
  private rewardService: SmileRewardService

  constructor() {
    this.authService = new AuthService()
    this.shopifyAuthService = new ShopifyAuthService()
    this.rewardService = new SmileRewardService()
  }

  async updateReceivePostNotifications({ request, response }: HttpContext) {
    const { userId, receivePostNotifications } = request.body()

    try {
      const user = await ZnUser.findOrFail(userId)
      user.receivePostNotifications = receivePostNotifications
      await user.save()

      return response.json({
        success: true,
        message: 'Notification preference updated successfully',
        data: user,
      })
    } catch (error) {
      console.log(error)

      return response.status(400).json({
        success: false,
        message: 'Failed to update notification preference',
        error: error.message,
      })
    }
  }

  /**
   * @index
   * @tag Admin User Management
   * @summary Read all users
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnUser[]>.append("id":"").paginated() - Read all users descriptively
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.USER)

    try {
      const { page = 1, limit = 10, search, fields, filter, sort } = request.qs()

      const query = ZnUser.query().preload('avatarMedia')

      if (search) {
        if (fields) {
          query.where((queryBuilder) => {
            fields.map((field: string) =>
              queryBuilder.orWhereRaw(`LOWER(${field}) LIKE LOWER(?)`, [`%${search}%`])
            )
          })
        } else {
          query.where((queryBuilder) => {
            queryBuilder
              .whereRaw('LOWER(email) LIKE LOWER(?)', [`%${search}%`])
              .orWhereRaw('LOWER(firstName) LIKE LOWER(?)', [`%${search}%`])
              .orWhereRaw('LOWER(lastName) LIKE LOWER(?)', [`%${search}%`])
          })
        }
      }

      if (filter) {
        query.where((queryBuilder) => {
          filter.map((fil: string) =>
            queryBuilder.orWhereRaw(`LOWER(${fil.split('=')[0]}) LIKE LOWER(?)`, [
              `%${fil.split('=')[1]}%`,
            ])
          )
        })
      }

      if (sort) {
        if (sort.length > 0) {
          query.orderBy(sort[0], sort[1])
        }
      } else {
        query.orderBy('updatedAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)

      return response.internalServerError(error)
    }
  }

  /**
   * @filter
   * @tag Admin User Management
   * @summary Filter users
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnUser[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read user selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async filter({ request, response }: HttpContext) {
    const { search } = request.qs()

    try {
      const query = ZnUser.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(email) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(firstName) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(LastName) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      const result = await query.limit(5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @select
   * @tag Admin User Management
   * @summary Read all users for selection
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnUser[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read user selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async select({ request, response }: HttpContext) {
    const { page = 1, search, filter } = request.qs()

    try {
      const query = ZnUser.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(email) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(firstName) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(lastName) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  async getNonAffiliteUsers({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.USER)
      const { page = 1, limit = 10, search } = request.qs();

      const query = ZnUser.query().doesntHave('affiliate');

      if (search && search.trim() !== '') {
        query.whereILike('email', `%${search}%`)
          .orWhereILike('firstName', `%${search}%`)
          .orWhereILike('lastName', `%${search}%`)
          .orWhereILike('phone', `%${search}%`);
      }

      return await query.paginate(page, limit);

    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @show
   * @tag Admin User Management
   * @summary Read a user
   * @paramPath id - ID of User - @type(string) @required
   * @responseBody 200 - <ZnUser>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a user descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"User not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.USER)

    const userId = params.id

    const user = await ZnUser.query().where('id', userId).preload('avatarMedia').first()

    if (!user) {
      return response.notFound({ message: 'User not found' })
    }

    return response.ok(user)
  }

  /**
   * @create
   * @tag Admin User Management
   * @summary Return info for creation
   * @responseBody 200 - {"data":{"roles":["ZnRole"]}} - Return info for creation descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.USER)

    try {
      // const roles = await Zn.query().whereNull('deletedAt')

      return response.ok({
        // options: {
        //   roles: roles,
        // },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin User Management
   * @summary Create action
   * @requestBody <ZnUser>.only(email, firstName, lastName)
   * @responseBody 201 - <ZnAdmin>.append("id":"","createdAt":"","updatedAt":"") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The email field must be defined","rule":"required","field":"email"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.USER)

    const data = request.all()

    const payload = await createUserValidator.validate(data)

    try {
      const newUser: any = {
        firstName: payload.firstName,
        lastName: payload.lastName,
        email: payload.email,

        // avatar: payload.avatar,
        avatarId: payload.avatarId,

        phone: payload.phone,
        gender: payload.gender,
        birthday: DateTime.fromISO(payload.birthday || '').toJSDate(),

        mile: payload.mile,
        longitude: payload.longitude,
        latitude: payload.latitude,
        timezone: payload.timezone,

        stripeId: payload.stripeId,
        deviceToken: payload.deviceToken,

        // defaultAddressId: payload.defaultAddressId,

        active: payload.acive !== undefined ? payload.acive : (true as any),
      }

      if (payload.avatar) {
        newUser.avatar = payload.avatar
      } else if (payload.avatarId) {
        const avatarMedia = await ZnMedia.find(payload.avatarId)
        if (avatarMedia) {
          newUser.avatar = avatarMedia.fileKey
        }
      }

      const created = await this.authService.createUser(newUser)

      const shopifyCustomer = await this.shopifyAuthService.getCustomerByEmail(created.email)
      if (!shopifyCustomer) {
        await this.shopifyAuthService.createCustomer(created)
      }

      const { data } = await this.rewardService.fetchCustomerData(created.email)
      const discounts = await this.rewardService.getCustomerDiscounts(created)

      created.shopifyCustomerId = shopifyCustomer?.id
      created.rewardPoints = data?.customers[0]?.points_balance || 0
      created.noDiscount = Object.values(discounts.val() ?? [])?.length ?? 0

      const createdWithShopify = await created.save()

      return response.created(createdWithShopify)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @edit
   * @tag Admin User Management
   * @summary Return info for updating
   * @responseBody 200 - {"user":"<ZnUser>","data":{"roles":["ZnRole"]}} - Return info for updating descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"User not found"} - Not Found
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.USER)

    try {
      const userId = params.id

      const user = await ZnUser.query().where('id', userId).preload('avatarMedia').first()

      if (!user) {
        return response.notFound({ message: 'User not found' })
      }

      return response.ok({
        user: {
          ...user?.serialize(),
        },
        data: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin User Management
   * @summary Update user info
   * @description Update user info descriptively
   * @paramPath id - ID of User - @type(string) @required
   * @requestBody <ZnUser>
   * @responseBody 200 - <ZnUser>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The email field must be defined","rule":"required","field":"email"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"User not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.USER)

    const userId = params.id

    const userToUpdate = await ZnUser.query().where('id', userId).first()
    if (!userToUpdate) {
      return response.notFound({ message: 'User not found' })
    }

    const data = request.all()

    const payload = await updateUserValidator.validate(data)

    try {
      let shopifyCustomer
      shopifyCustomer = await this.shopifyAuthService.getCustomerByEmail(userToUpdate.email)
      if (!shopifyCustomer) {
        shopifyCustomer = await this.shopifyAuthService.createCustomer(userToUpdate)
      }

      const { success, user } = await this.shopifyAuthService.updateCustomer(shopifyCustomer.id, {
        id: shopifyCustomer.id,
        first_name: payload.firstName,
        last_name: payload.lastName,
        phone: payload.phone,
      })

      if (success) {
        userToUpdate.firstName = payload.firstName || userToUpdate.firstName
        userToUpdate.lastName = payload.lastName || userToUpdate.lastName

        // userToUpdate.avatar = payload.avatar || userToUpdate.avatar
        userToUpdate.avatarId = payload.avatarId || userToUpdate.avatarId

        if (payload.avatar) {
          userToUpdate.avatar = payload.avatar
        } else if (payload.avatarId) {
          const avatarMedia = await ZnMedia.find(payload.avatarId)
          if (avatarMedia) {
            userToUpdate.avatar = avatarMedia.fileKey
          }
        }

        userToUpdate.phone = payload.phone || userToUpdate.phone
        userToUpdate.gender = payload.gender || userToUpdate.gender
        userToUpdate.birthday =
          (payload.birthday && DateTime.fromISO(payload.birthday)) || userToUpdate.birthday

        userToUpdate.mile = payload.mile || userToUpdate.mile
        userToUpdate.latitude = payload.latitude || userToUpdate.latitude
        userToUpdate.longitude = payload.longitude || userToUpdate.longitude
        userToUpdate.timezone = payload.timezone || userToUpdate.timezone

        userToUpdate.stripeId = payload.stripeId || userToUpdate.stripeId

        userToUpdate.deviceToken = payload.deviceToken || userToUpdate.deviceToken

        userToUpdate.active =
          payload.active !== undefined && payload.active !== null
            ? payload.active
            : (userToUpdate.active as any)

        const { data } = await this.rewardService.fetchCustomerData(user.email)
        const discounts = await this.rewardService.getCustomerDiscounts(userToUpdate)

        userToUpdate.shopifyCustomerId = shopifyCustomer.id
        userToUpdate.rewardPoints =
          payload.rewardPoints ||
          data?.customers[0]?.points_balance ||
          0 ||
          userToUpdate.rewardPoints
        userToUpdate.noDiscount =
          (Object.values(discounts.val() ?? [])?.length ?? 0) || userToUpdate.noDiscount

        const updated = await userToUpdate.save()

        return response.ok(updated)
      }

      return response.abort('Something went wrong')
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin User Management
   * @summary Soft-delete a user
   * @description Soft-delete a user descriptively
   * @paramPath id - ID of User - @type(string) @required
   * @responseBody 200 - {"message":"User soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"User not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.USER)

    const userId = params.id

    const user = await ZnUser.find(userId)

    if (!user) {
      return response.notFound({ message: 'User not found' })
    }

    await user.softDelete()

    return response.ok({ message: 'User soft-deleted successfully' })
  }
}
