import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AffiliateController = () => import('#controllers/affiliate_controller')
const AffiliationTierController = () => import('#controllers/affiliate_tier_controller')
const PaymentMethodController = () => import('#controllers/payment_method_controller')
const AffiliationCommissionsController = () =>
  import('#controllers/affiliation_commissions_controller')

export default function affiliationRoutes() {
  router
    .group(() => {
      router.get('/refcode/:id', [AffiliateController, 'getRefCode'])
    })
    .prefix('affiliates')

  router
    .group(() => {
      router.post('/', [AffiliateController, 'store'])
      router.get('/stats', [AffiliateController, 'stats'])
      router.get('/video-report', [AffiliateController, 'videoReport'])
      router.get('/commissions', [AffiliateController, 'commissions'])
      router.get('/payouts', [AffiliateController, 'payouts'])
      router.get('/', [AffiliateController, 'show'])
      router.put('/', [AffiliateController, 'update'])
      router.patch('/', [AffiliateController, 'updateField'])
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
    .prefix('affiliates')

  router
    .group(() => {
      router.get('/:id/next', [AffiliationTierController, 'showNextTier'])
      router.get('/:id', [AffiliationTierController, 'show'])
      router.get('/', [AffiliationTierController, 'index'])
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
    .prefix('affiliate-tiers')

  router
    .group(() => {
      router.resource('/commissions', AffiliationCommissionsController)
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))

  router
    .group(() => {
      router.patch('/payment-methods/:id/set-default', [
        PaymentMethodController,
        'setDefaultMethod',
      ])
      router.resource('/payment-methods', PaymentMethodController)
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
}
