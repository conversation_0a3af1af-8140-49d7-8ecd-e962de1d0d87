import ZnUser from "#models/zn_user"
import VendorPaymentService from "#services/vendors/vendor_payment_service"
import { addPaymentMethodValidator, updatePaymentMethodValidator } from "#validators/affiliate"
import { HttpContext } from "@adonisjs/core/http"

export default class VendorPaymentMethodController {
  private vendorPaymentService: VendorPaymentService;

  constructor() {
    this.vendorPaymentService = new VendorPaymentService();
  }

  /**
   * @index
   * @tag Payment Methods for Vendor
   * @summary Get list of payment methods
   * @responseBody 201 - <ZnPaymentMethod[]>
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async index({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const result = await this.vendorPaymentService.getAllPaymentMethodsByUser(user);
      if (result.success) {
        return response.ok(result.paymentMethods);
      } else {
        return response.badRequest(result.message);
      }
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Payment Methods for Vendor
   * @summary Get a payment methods
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async show({ params, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      if (!paymentMethodId) return response.badRequest('Payment method ID is required')
      return await this.vendorPaymentService.getPaymentMethodById(paymentMethodId)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Payment Methods for Vendor
   * @summary Add new payment method for Vendor
   * @requestBody {"paymentType": "PAYPAL", "isDefault": true, "paypalEmail": ""}
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async store({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser;
      const payload = await addPaymentMethodValidator.validate(request.all());

      const result = await this.vendorPaymentService.create(user, payload);

      if (result.success) {
        return response.ok(result.paymentMethods);
      } else {
        return response.notModified(result.message);
      }

    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Payment Methods for Vendor
   * @summary Update payment method
   * @requestBody {"paypalEmail": "<EMAIL>"}
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async update({ params, request, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      if (!paymentMethodId) return response.badRequest('Payment method ID is required')

      const payload = await updatePaymentMethodValidator.validate(request.all())

      return this.vendorPaymentService.update(paymentMethodId, payload);

    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @setDefaultMethod
   * @tag Payment Methods for Vendor
   * @summary Set default payment method
   * @responseBody 201 - <ZnPaymentMethod[]>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async setDefaultMethod({ params, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      if (!paymentMethodId) return response.badRequest('Payment method ID is required')

      return this.vendorPaymentService.setDefaultMethod(paymentMethodId);

    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @destroy
   * @tag Payment Methods for Vendor
   * @summary Delete a payment method
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async destroy({ params, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      if (!paymentMethodId) return response.badRequest('Payment method ID is required')

      const message = await this.vendorPaymentService.delete(paymentMethodId);
      return response.ok(message);

    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}