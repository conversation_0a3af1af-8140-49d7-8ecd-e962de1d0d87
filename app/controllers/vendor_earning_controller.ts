import VendorService from "#services/vendors/vendor_service";
import { HttpContext } from "@adonisjs/core/http";
import VendorEarningService from "#services/vendors/vendor_earning_service";

export default class VendorEarningController {
  private vendorService: VendorService;
  private vendorEarningService: VendorEarningService;

  constructor() {
    this.vendorService = new VendorService();
    this.vendorEarningService = new VendorEarningService();
  }

  async index({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const vendor = await this.vendorService.getVendorByUserId(user.id, false);
      const earnings = await this.vendorEarningService.getAllEarningsByVendor(vendor.id);
      return response.ok(earnings);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async show({ auth, params, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const earningId = params.id;
      if (!earningId) {
        return response.badRequest('Earning ID is required.');
      }

      const earning = await this.vendorEarningService.getEarningById(earningId);

      return response.ok(earning);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }
}