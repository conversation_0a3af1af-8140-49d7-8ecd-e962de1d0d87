import type { HttpContext } from '@adonisjs/core/http'
import VendorService from "../../../services/vendors/vendor_service.js";
import { ModelPaginatorContract } from '@adonisjs/lucid/types/model';
import ZnUser from "#models/zn_user";
import ZnVendorOrder from "#models/zn_vendor_order";

export default class VendorOrdersController {
  private vendorService: VendorService

  constructor() {
    this.vendorService = new VendorService()
  }

  /**
   * Display a list of resource
   */
  async index({auth, request, response}: HttpContext) {
    //Get vendor id from login
    const user = auth.getUserOrFail() as ZnUser
    const {page, limit} = request.qs()

    //Get order by vendor id
    let data: never[] | ModelPaginatorContract<ZnVendorOrder> = []
    const vendor = await this.vendorService.getVendorByUserId(user?.id)

    if(vendor) {
      data = await this.vendorService.getVendorOrders(vendor.id, page, limit)
    }
    return response.ok(data)
    //Return data
  }


  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const orderId = params.id
    //Get order detail
    const order = await this.vendorService.getOrderById(orderId)
    if(order) {
      return response.ok(order)
    }

    return response.notFound("Order not found")
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const orderId = params.id
    console.log(orderId, request)
    return response.ok("OK")
  }

  /**
   * Delete record
   */
  async destroy({ params, response }: HttpContext) {
    const orderId = params.id
    const order = await this.vendorService.getOrderById(orderId)
    if(order) {
      return order.softDelete()
    }

    return response.notFound("Order not found")
  }
}
