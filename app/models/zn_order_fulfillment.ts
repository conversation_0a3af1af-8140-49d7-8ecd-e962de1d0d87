import { belongsTo, column, hasOne, manyToMany } from "@adonisjs/lucid/orm";
import AppModel from "./app_model.js";
import ZnOrder from "./zn_order.js";
import type { BelongsTo, HasOne, ManyToMany } from "@adonisjs/lucid/types/relations";
import ZnOrderDetail from "./zn_order_detail.js";
import ZnVendorOrder from "./zn_vendor_order.js";

export default class ZnOrderFulfillment extends AppModel {
  static table = 'zn_order_fulfillments';

  @column({ columnName: 'status' })
  declare status: string;

  @column({ columnName: 'trackingNumber' })
  declare trackingNumber: string;

  @column({ columnName: 'trackingNumbers' })
  declare trackingNumbers: string;

  @column({ columnName: 'trackingCompany' })
  declare trackingCompany: string

  @column({ columnName: 'trackingUrl' })
  declare trackingUrl: string

  @column({ columnName: 'trackingUrls' })
  declare trackingUrls: string

  @column({ columnName: 'shopifyFulfillmentId' })
  declare shopifyFulfillmentId: string

  @column({ columnName: 'orderId' })
  declare orderId: string;

  @belongsTo(() => ZnOrder, {
    foreignKey: 'orderId'
  })
  declare order: BelongsTo<typeof ZnOrder>;

  @manyToMany(() => ZnOrderDetail, {
    pivotTable: 'zn_order_fulfillments_and_order_details',
    pivotForeignKey: 'fulfillmentId',
    pivotRelatedForeignKey: 'orderDetailId',
    pivotColumns: ['quantity']
  })
  declare orderDetails: ManyToMany<typeof ZnOrderDetail>

  @hasOne(() => ZnVendorOrder, {
    foreignKey: 'fulfillmentId'
  })
  declare vendorOrders: HasOne<typeof ZnVendorOrder>
}