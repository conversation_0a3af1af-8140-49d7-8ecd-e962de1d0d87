import AppModel from '#models/app_model'
import { belongsTo, column } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon';
import ZnOrder from './zn_order.js';
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import ZnVendor from './zn_vendor.js';
import ZnOrderFulfillment from './zn_order_fulfillment.js';

export default class ZnVendorOrder extends AppModel {
  static table = 'zn_vendor_orders';

  @column({ columnName: 'status' })
  declare status: string

  @column.dateTime({ columnName: 'cancelledAt' })
  declare cancelledAt: DateTime

  @column.dateTime({ columnName: 'closedAt' })
  declare closedAt: DateTime

  @column({
    columnName: 'subtotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare subtotalPrice: number

  @column({
    columnName: 'totalTax',
    consume: (value: string) => parseFloat(value),
  })
  declare totalTax: number

  @column({
    columnName: 'totalDiscounts',
    consume: (value: string) => parseFloat(value),
  })
  declare totalDiscounts: number

  @column({
    columnName: 'totalShipping',
    consume: (value: string) => parseFloat(value),
  })
  declare totalShipping: number

  @column({
    columnName: 'totalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare totalPrice: number

  @column({
    columnName: 'currentTotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare currentTotalPrice: number

  @column({ columnName: 'note' })
  declare note: string

  @column({ columnName: 'orderId' })
  declare orderId: string

  @column({ columnName: 'vendorId' })
  declare vendorId: string

  @column({ columnName: 'fulfillmentId' })
  declare fulfillmentId: string

  @belongsTo(() => ZnOrder, {
    foreignKey: 'orderId'
  })
  declare order: BelongsTo<typeof ZnOrder>;

  @belongsTo(() => ZnVendor, {
    foreignKey: 'vendorId'
  })
  declare vendor: BelongsTo<typeof ZnVendor>;

  @belongsTo(() => ZnOrderFulfillment, {
    foreignKey: 'fulfillmentId'
  })
  declare fulfillment: BelongsTo<typeof ZnOrderFulfillment>;
}
